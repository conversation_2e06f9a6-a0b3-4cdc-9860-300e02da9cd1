{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OCR Analysis of diag.png\n", "\n", "This notebook performs OCR (Optical Character Recognition) on the image 'diag.png' using Tesseract OCR.\n", "The goal is to extract the text 'R768' from the image."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import pytesseract\n", "from PIL import Image, ImageEnhance, ImageFilter\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "# Configure Tesseract path\n", "pytesseract.pytesseract.tesseract_cmd = r'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Tesseract-OCR\\tesseract.exe'\n", "\n", "# Set image path\n", "image_path = 'diag.png'\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"Image path: {image_path}\")\n", "print(f\"Image exists: {os.path.exists(image_path)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON>ad and Display Original Image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the original image\n", "original_image = Image.open(image_path)\n", "original_cv = cv2.imread(image_path)\n", "\n", "print(f\"Image size: {original_image.size}\")\n", "print(f\"Image mode: {original_image.mode}\")\n", "\n", "# Display the original image\n", "plt.figure(figsize=(10, 6))\n", "plt.imshow(original_image)\n", "plt.title('Original Image')\n", "plt.axis('off')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Basic OCR Attempt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform basic OCR on the original image\n", "basic_text = pytesseract.image_to_string(original_image)\n", "print(\"Basic OCR Result:\")\n", "print(f\"'{basic_text.strip()}'\")\n", "print(f\"Target: 'R768'\")\n", "print(f\"Match: {basic_text.strip() == 'R768'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Image Preprocessing Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess_image_grayscale(image):\n", "    \"\"\"Convert image to grayscale\"\"\"\n", "    return image.convert('L')\n", "\n", "def preprocess_image_threshold(image, threshold=128):\n", "    \"\"\"Apply binary threshold to image\"\"\"\n", "    gray = image.convert('L')\n", "    return gray.point(lambda x: 0 if x < threshold else 255, '1')\n", "\n", "def preprocess_image_enhance_contrast(image, factor=2.0):\n", "    \"\"\"Enhance image contrast\"\"\"\n", "    enhancer = ImageEnhance.Contrast(image)\n", "    return enhancer.enhance(factor)\n", "\n", "def preprocess_image_resize(image, scale_factor=3):\n", "    \"\"\"Resize image by scale factor\"\"\"\n", "    width, height = image.size\n", "    new_size = (width * scale_factor, height * scale_factor)\n", "    return image.resize(new_size, Image.LANCZOS)\n", "\n", "def preprocess_opencv_morphology(image_path):\n", "    \"\"\"Apply morphological operations using OpenCV\"\"\"\n", "    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)\n", "    \n", "    # Apply Gaussian blur to reduce noise\n", "    blurred = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(img, (5, 5), 0)\n", "    \n", "    # Apply threshold\n", "    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)\n", "    \n", "    # Morphological operations\n", "    kernel = np.ones((2, 2), np.uint8)\n", "    opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)\n", "    closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel, iterations=1)\n", "    \n", "    return Image.fromarray(closing)\n", "\n", "print(\"Preprocessing functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test Different Preprocessing Techniques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different preprocessing techniques\n", "preprocessing_methods = [\n", "    (\"Original\", original_image),\n", "    (\"Grayscale\", preprocess_image_grayscale(original_image)),\n", "    (\"Binary Threshold (128)\", preprocess_image_threshold(original_image, 128)),\n", "    (\"Binary Threshold (100)\", preprocess_image_threshold(original_image, 100)),\n", "    (\"Enhanced Contrast (2x)\", preprocess_image_enhance_contrast(original_image, 2.0)),\n", "    (\"Enhanced Contrast (3x)\", preprocess_image_enhance_contrast(original_image, 3.0)),\n", "    (\"Resized (3x)\", preprocess_image_resize(original_image, 3)),\n", "    (\"OpenCV Morphology\", preprocess_opencv_morphology(image_path))\n", "]\n", "\n", "results = []\n", "\n", "for method_name, processed_image in preprocessing_methods:\n", "    try:\n", "        # Perform OCR\n", "        text = pytesseract.image_to_string(processed_image).strip()\n", "        \n", "        # Store result\n", "        results.append({\n", "            'method': method_name,\n", "            'text': text,\n", "            'match': text == 'R768'\n", "        })\n", "        \n", "        print(f\"{method_name}: '{text}' - Match: {text == 'R768'}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"{method_name}: Error - {str(e)}\")\n", "        results.append({\n", "            'method': method_name,\n", "            'text': f'Error: {str(e)}',\n", "            'match': <PERSON><PERSON><PERSON>\n", "        })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Advanced OCR with Different Tesseract Configurations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different Tesseract configurations\n", "tesseract_configs = [\n", "    ('Default', ''),\n", "    ('PSM 6 (Single block)', '--psm 6'),\n", "    ('PSM 7 (Single text line)', '--psm 7'),\n", "    ('PSM 8 (Single word)', '--psm 8'),\n", "    ('PSM 10 (Single character)', '--psm 10'),\n", "    ('PSM 13 (Raw line)', '--psm 13'),\n", "    ('Whitelist alphanumeric', '-c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),\n", "    ('OEM 1 (Neural nets)', '--oem 1'),\n", "    ('Combined: PSM 8 + Whitelist', '--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')\n", "]\n", "\n", "print(\"Testing different Tesseract configurations...\\n\")\n", "\n", "# Find the best preprocessing method from previous results\n", "best_methods = [result for result in results if result['match']]\n", "if best_methods:\n", "    print(f\"Found {len(best_methods)} successful preprocessing method(s)!\")\n", "    test_image = original_image  # We'll use the best method if found\n", "else:\n", "    print(\"No preprocessing method worked perfectly. Testing configurations on enhanced image...\")\n", "    # Use enhanced contrast + resize as it often works well\n", "    test_image = preprocess_image_resize(\n", "        preprocess_image_enhance_contrast(original_image, 2.0), 3\n", "    )\n", "\n", "config_results = []\n", "\n", "for config_name, config_string in tesseract_configs:\n", "    try:\n", "        if config_string:\n", "            text = pytesseract.image_to_string(test_image, config=config_string).strip()\n", "        else:\n", "            text = pytesseract.image_to_string(test_image).strip()\n", "        \n", "        config_results.append({\n", "            'config': config_name,\n", "            'text': text,\n", "            'match': text == 'R768'\n", "        })\n", "        \n", "        print(f\"{config_name}: '{text}' - Match: {text == 'R768'}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"{config_name}: Error - {str(e)}\")\n", "        config_results.append({\n", "            'config': config_name,\n", "            'text': f'Error: {str(e)}',\n", "            'match': <PERSON><PERSON><PERSON>\n", "        })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Combined Approach: Best Preprocessing + Best Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine the best preprocessing with the best configuration\n", "print(\"Testing combinations of preprocessing and configurations...\\n\")\n", "\n", "# Get successful configurations\n", "successful_configs = [result for result in config_results if result['match']]\n", "\n", "if successful_configs:\n", "    print(f\"Found {len(successful_configs)} successful configuration(s)!\")\n", "    print(\"Success! The OCR is working correctly.\")\n", "else:\n", "    print(\"Testing more combinations...\")\n", "    \n", "    # Test all combinations of preprocessing and configurations\n", "    best_combinations = []\n", "    \n", "    for method_name, processed_image in preprocessing_methods[:6]:  # Test top 6 methods\n", "        for config_name, config_string in tesseract_configs[:5]:  # Test top 5 configs\n", "            try:\n", "                if config_string:\n", "                    text = pytesseract.image_to_string(processed_image, config=config_string).strip()\n", "                else:\n", "                    text = pytesseract.image_to_string(processed_image).strip()\n", "                \n", "                if text == 'R768':\n", "                    best_combinations.append({\n", "                        'preprocessing': method_name,\n", "                        'config': config_name,\n", "                        'text': text\n", "                    })\n", "                    print(f\"SUCCESS: {method_name} + {config_name} = '{text}'\")\n", "                    \n", "            except Exception as e:\n", "                continue\n", "    \n", "    if best_combinations:\n", "        print(f\"\\nFound {len(best_combinations)} successful combination(s)!\")\n", "    else:\n", "        print(\"\\nNo perfect matches found. Let's see the closest results...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Final Results Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== FINAL RESULTS SUMMARY ===\")\n", "print(f\"Target text: 'R768'\\n\")\n", "\n", "# Show all successful results\n", "all_successful = []\n", "\n", "# From preprocessing tests\n", "for result in results:\n", "    if result['match']:\n", "        all_successful.append(f\"Preprocessing: {result['method']} -> '{result['text']}'\")\n", "\n", "# From configuration tests\n", "for result in config_results:\n", "    if result['match']:\n", "        all_successful.append(f\"Configuration: {result['config']} -> '{result['text']}'\")\n", "\n", "# From combination tests (if any)\n", "if 'best_combinations' in locals():\n", "    for combo in best_combinations:\n", "        all_successful.append(f\"Combination: {combo['preprocessing']} + {combo['config']} -> '{combo['text']}'\")\n", "\n", "if all_successful:\n", "    print(\"✅ SUCCESSFUL METHODS:\")\n", "    for success in all_successful:\n", "        print(f\"  {success}\")\n", "    print(f\"\\n🎉 OCR successfully extracted 'R768' from the image!\")\n", "else:\n", "    print(\"❌ No method successfully extracted 'R768'\")\n", "    print(\"\\nClosest results:\")\n", "    \n", "    # Show closest matches\n", "    all_results = results + config_results\n", "    for result in all_results:\n", "        if 'text' in result and result['text'] and not result['text'].startswith('Error'):\n", "            print(f\"  {result.get('method', result.get('config', 'Unknown'))}: '{result['text']}'\")\n", "\n", "print(\"\\n=== END OF ANALYSIS ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Display Processed Images (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display some of the processed images for visual inspection\n", "fig, axes = plt.subplots(2, 4, figsize=(16, 8))\n", "axes = axes.flatten()\n", "\n", "for i, (method_name, processed_image) in enumerate(preprocessing_methods[:8]):\n", "    axes[i].imshow(processed_image, cmap='gray' if processed_image.mode in ['L', '1'] else None)\n", "    axes[i].set_title(method_name)\n", "    axes[i].axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}