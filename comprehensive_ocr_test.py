#!/usr/bin/env python3
"""
Comprehensive OCR test script for diag.png
This script implements all the techniques from the Jupyter notebook
"""

import os
import sys
import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter

# Configure Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'

def preprocess_image_grayscale(image):
    """Convert image to grayscale"""
    return image.convert('L')

def preprocess_image_threshold(image, threshold=128):
    """Apply binary threshold to image"""
    gray = image.convert('L')
    return gray.point(lambda x: 0 if x < threshold else 255, '1')

def preprocess_image_enhance_contrast(image, factor=2.0):
    """Enhance image contrast"""
    enhancer = ImageEnhance.Contrast(image)
    return enhancer.enhance(factor)

def preprocess_image_resize(image, scale_factor=3):
    """Resize image by scale factor"""
    width, height = image.size
    new_size = (width * scale_factor, height * scale_factor)
    return image.resize(new_size, Image.LANCZOS)

def preprocess_opencv_morphology(image_path):
    """Apply morphological operations using OpenCV"""
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(img, (5, 5), 0)
    
    # Apply threshold
    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # Morphological operations
    kernel = np.ones((2, 2), np.uint8)
    opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
    closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel, iterations=1)
    
    return Image.fromarray(closing)

def preprocess_opencv_advanced(image_path):
    """Advanced OpenCV preprocessing"""
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    # Apply different techniques
    techniques = []
    
    # 1. Gaussian blur + OTSU threshold
    blurred = cv2.GaussianBlur(img, (3, 3), 0)
    _, otsu = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    techniques.append(("OTSU", Image.fromarray(otsu)))
    
    # 2. Adaptive threshold
    adaptive = cv2.adaptiveThreshold(img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    techniques.append(("Adaptive", Image.fromarray(adaptive)))
    
    # 3. Erosion + Dilation
    kernel = np.ones((2, 2), np.uint8)
    eroded = cv2.erode(img, kernel, iterations=1)
    dilated = cv2.dilate(eroded, kernel, iterations=1)
    techniques.append(("Erode+Dilate", Image.fromarray(dilated)))
    
    return techniques

def main():
    print("🚀 Comprehensive OCR Test")
    print("=" * 50)
    
    # Check setup
    image_path = 'diag.png'
    if not os.path.exists(image_path):
        print(f"❌ Image file '{image_path}' not found!")
        sys.exit(1)
    
    print(f"✅ Image file found: {image_path}")
    
    # Load image
    try:
        original_image = Image.open(image_path)
        print(f"✅ Image loaded! Size: {original_image.size}, Mode: {original_image.mode}")
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        sys.exit(1)
    
    target = 'R768'
    successful_methods = []
    all_results = []
    
    print(f"\n🎯 Target: '{target}'")
    print("\n" + "="*50)
    
    # Test 1: Basic OCR
    print("🔍 Testing Basic OCR...")
    try:
        basic_text = pytesseract.image_to_string(original_image).strip()
        print(f"  Result: '{basic_text}' - Match: {basic_text == target}")
        all_results.append(("Basic OCR", basic_text))
        if basic_text == target:
            successful_methods.append("Basic OCR")
    except Exception as e:
        print(f"  Error: {e}")
    
    # Test 2: Preprocessing methods
    print("\n🔍 Testing Preprocessing Methods...")
    preprocessing_methods = [
        ("Grayscale", lambda img: preprocess_image_grayscale(img)),
        ("Binary Threshold (128)", lambda img: preprocess_image_threshold(img, 128)),
        ("Binary Threshold (100)", lambda img: preprocess_image_threshold(img, 100)),
        ("Binary Threshold (150)", lambda img: preprocess_image_threshold(img, 150)),
        ("Enhanced Contrast (2x)", lambda img: preprocess_image_enhance_contrast(img, 2.0)),
        ("Enhanced Contrast (3x)", lambda img: preprocess_image_enhance_contrast(img, 3.0)),
        ("Enhanced Contrast (4x)", lambda img: preprocess_image_enhance_contrast(img, 4.0)),
        ("Resized (2x)", lambda img: preprocess_image_resize(img, 2)),
        ("Resized (3x)", lambda img: preprocess_image_resize(img, 3)),
        ("Resized (4x)", lambda img: preprocess_image_resize(img, 4)),
        ("Resized (5x)", lambda img: preprocess_image_resize(img, 5)),
        ("OpenCV Morphology", lambda img: preprocess_opencv_morphology(image_path)),
    ]
    
    for method_name, preprocess_func in preprocessing_methods:
        try:
            processed_image = preprocess_func(original_image)
            text = pytesseract.image_to_string(processed_image).strip()
            print(f"  {method_name}: '{text}' - Match: {text == target}")
            all_results.append((method_name, text))
            if text == target:
                successful_methods.append(method_name)
        except Exception as e:
            print(f"  {method_name}: Error - {e}")
    
    # Test 3: Advanced OpenCV techniques
    print("\n🔍 Testing Advanced OpenCV Techniques...")
    try:
        opencv_techniques = preprocess_opencv_advanced(image_path)
        for technique_name, processed_image in opencv_techniques:
            try:
                text = pytesseract.image_to_string(processed_image).strip()
                print(f"  OpenCV {technique_name}: '{text}' - Match: {text == target}")
                all_results.append((f"OpenCV {technique_name}", text))
                if text == target:
                    successful_methods.append(f"OpenCV {technique_name}")
            except Exception as e:
                print(f"  OpenCV {technique_name}: Error - {e}")
    except Exception as e:
        print(f"  OpenCV techniques failed: {e}")
    
    # Test 4: Tesseract configurations
    print("\n🔍 Testing Tesseract Configurations...")
    tesseract_configs = [
        ('PSM 6 (Single block)', '--psm 6'),
        ('PSM 7 (Single text line)', '--psm 7'),
        ('PSM 8 (Single word)', '--psm 8'),
        ('PSM 10 (Single character)', '--psm 10'),
        ('PSM 13 (Raw line)', '--psm 13'),
        ('Whitelist alphanumeric', '-c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('OEM 1 (Neural nets)', '--oem 1'),
        ('PSM 8 + Whitelist', '--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('PSM 7 + Whitelist', '--psm 7 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
    ]
    
    # Use enhanced + resized image for config testing
    test_image = preprocess_image_resize(
        preprocess_image_enhance_contrast(original_image, 3.0), 4
    )
    
    for config_name, config_string in tesseract_configs:
        try:
            text = pytesseract.image_to_string(test_image, config=config_string).strip()
            print(f"  {config_name}: '{text}' - Match: {text == target}")
            all_results.append((config_name, text))
            if text == target:
                successful_methods.append(config_name)
        except Exception as e:
            print(f"  {config_name}: Error - {e}")
    
    # Test 5: Combination testing
    print("\n🔍 Testing Combinations...")
    if not successful_methods:
        # Test promising combinations
        combinations = [
            ("Grayscale + PSM 8", preprocess_image_grayscale(original_image), '--psm 8'),
            ("Enhanced 3x + Resized 4x + PSM 8", 
             preprocess_image_resize(preprocess_image_enhance_contrast(original_image, 3.0), 4), '--psm 8'),
            ("Enhanced 4x + Resized 5x + Whitelist", 
             preprocess_image_resize(preprocess_image_enhance_contrast(original_image, 4.0), 5), 
             '-c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
            ("Threshold 100 + Resized 3x + PSM 7", 
             preprocess_image_resize(preprocess_image_threshold(original_image, 100), 3), '--psm 7'),
        ]
        
        for combo_name, processed_image, config in combinations:
            try:
                text = pytesseract.image_to_string(processed_image, config=config).strip()
                print(f"  {combo_name}: '{text}' - Match: {text == target}")
                all_results.append((combo_name, text))
                if text == target:
                    successful_methods.append(combo_name)
            except Exception as e:
                print(f"  {combo_name}: Error - {e}")
    
    # Results summary
    print("\n" + "="*50)
    print("📊 RESULTS SUMMARY")
    print("="*50)
    
    if successful_methods:
        print(f"🎉 SUCCESS! Found {len(successful_methods)} method(s) that extracted '{target}':")
        for method in successful_methods:
            print(f"  ✅ {method}")
    else:
        print(f"❌ No method successfully extracted '{target}'")
        print("\n📋 All results (closest matches):")
        
        # Show unique results sorted by similarity to target
        unique_results = {}
        for method, result in all_results:
            if result and not result.startswith('Error') and result not in unique_results:
                unique_results[result] = method
        
        # Sort by length and content similarity
        sorted_results = sorted(unique_results.items(), 
                              key=lambda x: (len(x[0]), -sum(1 for a, b in zip(x[0], target) if a == b)), 
                              reverse=True)
        
        for result, method in sorted_results[:10]:  # Show top 10
            print(f"  '{result}' - from {method}")
    
    print(f"\n📈 Total methods tested: {len(all_results)}")
    print("✅ Comprehensive OCR test completed!")

if __name__ == "__main__":
    main()
