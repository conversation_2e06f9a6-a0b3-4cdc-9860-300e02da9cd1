#!/usr/bin/env python3
"""
Simple OCR test without NumPy dependencies
This script tests basic OCR functionality using only PIL and pytesseract
"""

import os
import sys

# Configure Tesseract path
tesseract_path = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'

print("🚀 Simple OCR Test")
print("=" * 40)

# Check if Tesseract exists
if not os.path.exists(tesseract_path):
    print(f"❌ Tesseract not found at: {tesseract_path}")
    print("Please verify the Tesseract installation path.")
    sys.exit(1)
else:
    print(f"✅ Tesseract found at: {tesseract_path}")

# Check if image exists
image_path = 'diag.png'
if not os.path.exists(image_path):
    print(f"❌ Image file '{image_path}' not found!")
    sys.exit(1)
else:
    print(f"✅ Image file '{image_path}' found!")

# Try to import required libraries
try:
    from PIL import Image, ImageEnhance
    print("✅ PIL imported successfully!")
except ImportError as e:
    print(f"❌ PIL import failed: {e}")
    sys.exit(1)

try:
    import pytesseract
    pytesseract.pytesseract.tesseract_cmd = tesseract_path
    print("✅ pytesseract imported and configured!")
    
    # Test Tesseract version
    version = pytesseract.get_tesseract_version()
    print(f"✅ Tesseract version: {version}")
    
except ImportError as e:
    print(f"❌ pytesseract import failed: {e}")
    print("This might be due to NumPy compatibility issues.")
    print("Try installing in a clean environment:")
    print("1. conda create -n ocr python=3.9")
    print("2. conda activate ocr") 
    print("3. pip install pytesseract pillow")
    sys.exit(1)
except Exception as e:
    print(f"❌ pytesseract configuration failed: {e}")
    sys.exit(1)

# Load and test the image
try:
    print(f"\n🔍 Loading image: {image_path}")
    original_image = Image.open(image_path)
    print(f"✅ Image loaded! Size: {original_image.size}, Mode: {original_image.mode}")
    
    # Test basic OCR
    print("\n🔍 Testing basic OCR...")
    basic_text = pytesseract.image_to_string(original_image).strip()
    print(f"Basic OCR result: '{basic_text}'")
    print(f"Target: 'R768'")
    print(f"Match: {basic_text == 'R768'}")
    
    if basic_text == 'R768':
        print("🎉 SUCCESS! Basic OCR extracted 'R768' correctly!")
        sys.exit(0)
    
    # Try some simple preprocessing
    print("\n🔍 Trying simple preprocessing...")
    
    # Method 1: Grayscale
    try:
        gray_image = original_image.convert('L')
        gray_text = pytesseract.image_to_string(gray_image).strip()
        print(f"Grayscale: '{gray_text}' - Match: {gray_text == 'R768'}")
        if gray_text == 'R768':
            print("🎉 SUCCESS! Grayscale preprocessing worked!")
            sys.exit(0)
    except Exception as e:
        print(f"Grayscale failed: {e}")
    
    # Method 2: Enhanced contrast
    try:
        enhancer = ImageEnhance.Contrast(original_image)
        enhanced_image = enhancer.enhance(2.0)
        enhanced_text = pytesseract.image_to_string(enhanced_image).strip()
        print(f"Enhanced contrast: '{enhanced_text}' - Match: {enhanced_text == 'R768'}")
        if enhanced_text == 'R768':
            print("🎉 SUCCESS! Enhanced contrast worked!")
            sys.exit(0)
    except Exception as e:
        print(f"Enhanced contrast failed: {e}")
    
    # Method 3: Resized
    try:
        width, height = original_image.size
        resized_image = original_image.resize((width * 3, height * 3), Image.LANCZOS)
        resized_text = pytesseract.image_to_string(resized_image).strip()
        print(f"Resized (3x): '{resized_text}' - Match: {resized_text == 'R768'}")
        if resized_text == 'R768':
            print("🎉 SUCCESS! Resizing worked!")
            sys.exit(0)
    except Exception as e:
        print(f"Resizing failed: {e}")
    
    # Method 4: Different Tesseract config
    try:
        config_text = pytesseract.image_to_string(original_image, config='--psm 8').strip()
        print(f"PSM 8 (single word): '{config_text}' - Match: {config_text == 'R768'}")
        if config_text == 'R768':
            print("🎉 SUCCESS! PSM 8 configuration worked!")
            sys.exit(0)
    except Exception as e:
        print(f"PSM 8 failed: {e}")
    
    # Method 5: Character whitelist
    try:
        whitelist_text = pytesseract.image_to_string(
            original_image, 
            config='-c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        ).strip()
        print(f"Character whitelist: '{whitelist_text}' - Match: {whitelist_text == 'R768'}")
        if whitelist_text == 'R768':
            print("🎉 SUCCESS! Character whitelist worked!")
            sys.exit(0)
    except Exception as e:
        print(f"Character whitelist failed: {e}")
    
    print("\n📊 Summary:")
    print("- Basic OCR and simple preprocessing methods were tested")
    print("- None of the methods successfully extracted 'R768'")
    print("- The image might need more advanced preprocessing")
    print("- Try running the full Jupyter notebook for more options")
    
    print(f"\nClosest results:")
    results = [basic_text, gray_text if 'gray_text' in locals() else '', 
               enhanced_text if 'enhanced_text' in locals() else '',
               resized_text if 'resized_text' in locals() else '',
               config_text if 'config_text' in locals() else '',
               whitelist_text if 'whitelist_text' in locals() else '']
    
    for i, result in enumerate(results):
        if result and result.strip():
            method_names = ['Basic', 'Grayscale', 'Enhanced', 'Resized', 'PSM 8', 'Whitelist']
            print(f"  {method_names[i]}: '{result}'")

except Exception as e:
    print(f"❌ Error during OCR testing: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n✅ OCR test completed!")
print("For more advanced analysis, try setting up a clean environment and running the Jupyter notebook.")
