#!/usr/bin/env python3
"""
Final OCR optimization script
Focuses on the most promising approaches and tries additional fine-tuning
"""

import os
import sys
import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter

# Configure Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'

def create_optimized_images(image_path):
    """Create multiple optimized versions of the image"""
    original = Image.open(image_path)
    img_cv = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    optimized_images = []
    
    # 1. Multiple resize factors with contrast enhancement
    for scale in [2, 3, 4, 5, 6, 8, 10]:
        enhanced = ImageEnhance.Contrast(original).enhance(2.5)
        resized = enhanced.resize((original.size[0] * scale, original.size[1] * scale), Image.LANCZOS)
        optimized_images.append((f"Enhanced+Resized({scale}x)", resized))
    
    # 2. Different contrast levels with optimal resize
    for contrast in [1.5, 2.0, 2.5, 3.0, 3.5, 4.0]:
        enhanced = ImageEnhance.Contrast(original).enhance(contrast)
        resized = enhanced.resize((original.size[0] * 4, original.size[1] * 4), Image.LANCZOS)
        optimized_images.append((f"Contrast({contrast})+Resize(4x)", resized))
    
    # 3. Brightness adjustments
    for brightness in [0.8, 1.0, 1.2, 1.5, 2.0]:
        enhanced = ImageEnhance.Brightness(original).enhance(brightness)
        contrast_enhanced = ImageEnhance.Contrast(enhanced).enhance(2.5)
        resized = contrast_enhanced.resize((original.size[0] * 4, original.size[1] * 4), Image.LANCZOS)
        optimized_images.append((f"Brightness({brightness})+Contrast+Resize", resized))
    
    # 4. Sharpness adjustments
    for sharpness in [1.0, 1.5, 2.0, 3.0]:
        enhanced = ImageEnhance.Sharpness(original).enhance(sharpness)
        contrast_enhanced = ImageEnhance.Contrast(enhanced).enhance(2.5)
        resized = contrast_enhanced.resize((original.size[0] * 4, original.size[1] * 4), Image.LANCZOS)
        optimized_images.append((f"Sharpness({sharpness})+Contrast+Resize", resized))
    
    # 5. OpenCV advanced preprocessing
    # Gaussian blur variations
    for blur_size in [(3, 3), (5, 5), (7, 7)]:
        blurred = cv2.GaussianBlur(img_cv, blur_size, 0)
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        # Resize
        height, width = thresh.shape
        resized_cv = cv2.resize(thresh, (width * 4, height * 4), interpolation=cv2.INTER_CUBIC)
        optimized_images.append((f"Blur{blur_size}+OTSU+Resize", Image.fromarray(resized_cv)))
    
    # 6. Morphological operations with different kernels
    for kernel_size in [1, 2, 3]:
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        # Opening (erosion followed by dilation)
        opening = cv2.morphologyEx(img_cv, cv2.MORPH_OPEN, kernel)
        # Resize
        height, width = opening.shape
        resized_cv = cv2.resize(opening, (width * 4, height * 4), interpolation=cv2.INTER_CUBIC)
        optimized_images.append((f"Opening(kernel{kernel_size})+Resize", Image.fromarray(resized_cv)))
        
        # Closing (dilation followed by erosion)
        closing = cv2.morphologyEx(img_cv, cv2.MORPH_CLOSE, kernel)
        resized_cv = cv2.resize(closing, (width * 4, height * 4), interpolation=cv2.INTER_CUBIC)
        optimized_images.append((f"Closing(kernel{kernel_size})+Resize", Image.fromarray(resized_cv)))
    
    # 7. Edge enhancement
    # Unsharp mask
    blurred = cv2.GaussianBlur(img_cv, (0, 0), 2.0)
    unsharp = cv2.addWeighted(img_cv, 1.5, blurred, -0.5, 0)
    height, width = unsharp.shape
    resized_cv = cv2.resize(unsharp, (width * 4, height * 4), interpolation=cv2.INTER_CUBIC)
    optimized_images.append(("UnsharpMask+Resize", Image.fromarray(resized_cv)))
    
    return optimized_images

def test_tesseract_configs():
    """Get comprehensive list of Tesseract configurations to test"""
    configs = [
        ('Default', ''),
        ('PSM 6', '--psm 6'),
        ('PSM 7', '--psm 7'),
        ('PSM 8', '--psm 8'),
        ('PSM 10', '--psm 10'),
        ('PSM 13', '--psm 13'),
        ('Whitelist', '-c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('PSM 6 + Whitelist', '--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('PSM 7 + Whitelist', '--psm 7 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('PSM 8 + Whitelist', '--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('PSM 10 + Whitelist', '--psm 10 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ('OEM 1', '--oem 1'),
        ('OEM 1 + PSM 8', '--oem 1 --psm 8'),
        ('OEM 1 + PSM 8 + Whitelist', '--oem 1 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
    ]
    return configs

def main():
    print("🚀 Final OCR Optimization")
    print("=" * 60)
    
    image_path = 'diag.png'
    target = 'R768'
    
    if not os.path.exists(image_path):
        print(f"❌ Image file '{image_path}' not found!")
        sys.exit(1)
    
    print(f"✅ Target: '{target}'")
    print(f"✅ Image: {image_path}")
    
    # Create optimized images
    print("\n🔧 Creating optimized image variations...")
    optimized_images = create_optimized_images(image_path)
    print(f"✅ Created {len(optimized_images)} optimized variations")
    
    # Get Tesseract configurations
    configs = test_tesseract_configs()
    print(f"✅ Testing {len(configs)} Tesseract configurations")
    
    print(f"\n🔍 Testing {len(optimized_images)} × {len(configs)} = {len(optimized_images) * len(configs)} combinations...")
    print("=" * 60)
    
    successful_combinations = []
    close_matches = []
    all_results = {}
    
    # Test all combinations
    for img_name, img in optimized_images:
        for config_name, config_string in configs:
            try:
                if config_string:
                    text = pytesseract.image_to_string(img, config=config_string).strip()
                else:
                    text = pytesseract.image_to_string(img).strip()
                
                combination_name = f"{img_name} + {config_name}"
                
                if text == target:
                    successful_combinations.append(combination_name)
                    print(f"🎉 SUCCESS: {combination_name} -> '{text}'")
                elif text and len(text) >= 3:  # Only consider substantial results
                    # Check for close matches
                    similarity = sum(1 for a, b in zip(text, target) if a == b)
                    if similarity >= 2 or 'R' in text and ('7' in text or '6' in text or '8' in text):
                        close_matches.append((combination_name, text, similarity))
                        if len(close_matches) <= 10:  # Show first 10 close matches
                            print(f"📍 Close: {combination_name} -> '{text}' (similarity: {similarity})")
                
                # Store all non-empty results
                if text and text not in all_results:
                    all_results[text] = combination_name
                    
            except Exception as e:
                continue  # Skip errors silently for cleaner output
    
    # Results summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if successful_combinations:
        print(f"🎉 SUCCESS! Found {len(successful_combinations)} combination(s) that extracted '{target}':")
        for combo in successful_combinations:
            print(f"  ✅ {combo}")
    else:
        print(f"❌ No combination successfully extracted '{target}'")
        
        if close_matches:
            print(f"\n📍 Closest matches (top 15):")
            # Sort by similarity and show best matches
            close_matches.sort(key=lambda x: x[2], reverse=True)
            for combo, text, similarity in close_matches[:15]:
                print(f"  '{text}' - {combo} (similarity: {similarity})")
        
        print(f"\n📋 All unique results:")
        # Sort results by length and content
        sorted_results = sorted(all_results.items(), 
                              key=lambda x: (len(x[0]), -sum(1 for a, b in zip(x[0], target) if a == b)), 
                              reverse=True)
        
        for text, combo in sorted_results[:20]:  # Show top 20
            print(f"  '{text}' - {combo}")
    
    print(f"\n📈 Total combinations tested: {len(optimized_images) * len(configs)}")
    print(f"📈 Unique results found: {len(all_results)}")
    print("✅ Final optimization completed!")
    
    if not successful_combinations:
        print(f"\n💡 Recommendations:")
        print("1. The image might need manual editing to improve clarity")
        print("2. Try different image formats or scanning settings")
        print("3. Consider using a different OCR engine")
        print("4. The text might not actually be 'R768' - verify the image content")

if __name__ == "__main__":
    main()
