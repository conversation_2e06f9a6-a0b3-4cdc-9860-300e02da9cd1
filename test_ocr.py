#!/usr/bin/env python3
"""
Simple OCR test script for diag.png
This script tests if OCR can extract 'R768' from the image.
"""

import sys
import os

try:
    import pytesseract
    from PIL import Image, ImageEnhance
    import cv2
    import numpy as np
    print("✅ All required libraries imported successfully!")
except ImportError as e:
    print(f"❌ Missing required library: {e}")
    print("Please install required packages using:")
    print("conda install -c conda-forge pytesseract opencv pillow matplotlib")
    sys.exit(1)

# Configure Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'

# Check if image exists
image_path = 'diag.png'
if not os.path.exists(image_path):
    print(f"❌ Image file '{image_path}' not found!")
    sys.exit(1)

print(f"✅ Image file '{image_path}' found!")

# Load image
try:
    original_image = Image.open(image_path)
    print(f"✅ Image loaded successfully! Size: {original_image.size}, Mode: {original_image.mode}")
except Exception as e:
    print(f"❌ Error loading image: {e}")
    sys.exit(1)

# Test basic OCR
print("\n🔍 Testing basic OCR...")
try:
    basic_text = pytesseract.image_to_string(original_image).strip()
    print(f"Basic OCR result: '{basic_text}'")
    if basic_text == 'R768':
        print("🎉 SUCCESS! Basic OCR extracted 'R768' correctly!")
        sys.exit(0)
    else:
        print("⚠️ Basic OCR didn't get 'R768'. Trying preprocessing...")
except Exception as e:
    print(f"❌ Error during basic OCR: {e}")
    print("⚠️ Trying preprocessing...")

# Try different preprocessing techniques
preprocessing_methods = [
    ("Grayscale", lambda img: img.convert('L')),
    ("Enhanced Contrast (2x)", lambda img: ImageEnhance.Contrast(img).enhance(2.0)),
    ("Enhanced Contrast (3x)", lambda img: ImageEnhance.Contrast(img).enhance(3.0)),
    ("Resized (3x)", lambda img: img.resize((img.size[0]*3, img.size[1]*3), Image.LANCZOS)),
    ("Binary Threshold", lambda img: img.convert('L').point(lambda x: 0 if x < 128 else 255, '1')),
]

print("\n🔍 Testing preprocessing methods...")
success_found = False

for method_name, preprocess_func in preprocessing_methods:
    try:
        processed_image = preprocess_func(original_image)
        text = pytesseract.image_to_string(processed_image).strip()
        print(f"{method_name}: '{text}'")
        
        if text == 'R768':
            print(f"🎉 SUCCESS! {method_name} extracted 'R768' correctly!")
            success_found = True
            break
            
    except Exception as e:
        print(f"{method_name}: Error - {e}")

# Try different Tesseract configurations if preprocessing didn't work
if not success_found:
    print("\n🔍 Testing Tesseract configurations...")
    
    # Use enhanced contrast + resize as base image
    try:
        enhanced_image = ImageEnhance.Contrast(original_image).enhance(2.0)
        resized_image = enhanced_image.resize((enhanced_image.size[0]*3, enhanced_image.size[1]*3), Image.LANCZOS)
        
        configs = [
            ('PSM 6 (Single block)', '--psm 6'),
            ('PSM 7 (Single text line)', '--psm 7'),
            ('PSM 8 (Single word)', '--psm 8'),
            ('PSM 10 (Single character)', '--psm 10'),
            ('Whitelist alphanumeric', '-c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
            ('PSM 8 + Whitelist', '--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'),
        ]
        
        for config_name, config_string in configs:
            try:
                text = pytesseract.image_to_string(resized_image, config=config_string).strip()
                print(f"{config_name}: '{text}'")
                
                if text == 'R768':
                    print(f"🎉 SUCCESS! {config_name} extracted 'R768' correctly!")
                    success_found = True
                    break
                    
            except Exception as e:
                print(f"{config_name}: Error - {e}")
                
    except Exception as e:
        print(f"Error during configuration testing: {e}")

if success_found:
    print("\n✅ OCR setup is working correctly!")
    print("You can now run the Jupyter notebook 'ocr_diag.ipynb' for detailed analysis.")
else:
    print("\n⚠️ No method successfully extracted 'R768'.")
    print("The Jupyter notebook will provide more detailed analysis and visualization.")
    print("You may need to:")
    print("1. Check if the image contains the expected text")
    print("2. Try manual image preprocessing")
    print("3. Verify Tesseract installation")
