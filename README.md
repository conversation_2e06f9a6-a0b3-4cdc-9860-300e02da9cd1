# OCR Analysis for diag.png

This project performs Optical Character Recognition (OCR) on the image `diag.png` to extract the text "R768" using Tesseract OCR.

## Files Created

1. **`ocr_diag.ipynb`** - Main Jupyter notebook with comprehensive OCR analysis
2. **`test_ocr.py`** - Quick test script to verify OCR setup
3. **`setup_ocr_environment.py`** - Environment setup helper script
4. **`requirements.txt`** - Required Python packages
5. **`README.md`** - This documentation file

## Quick Start

### Option 1: Automatic Setup (Recommended)
```bash
# Run the setup script
python setup_ocr_environment.py

# Test the OCR
python test_ocr.py

# Run the full analysis
jupyter notebook ocr_diag.ipynb
```

### Option 2: Manual Setup

#### Step 1: Create a new conda environment (recommended)
```bash
# Open Anaconda Prompt and run:
conda create -n ocr python=3.9
conda activate ocr
```

#### Step 2: Install required packages
```bash
# Install via conda (preferred)
conda install -c conda-forge pytesseract opencv pillow matplotlib jupyter

# OR install via pip
pip install pytesseract opencv-python pillow matplotlib jupyter
```

#### Step 3: Fix NumPy compatibility (if needed)
```bash
# If you encounter NumPy version errors:
conda install numpy=1.24 -y
# OR
pip install numpy==1.24.3
```

#### Step 4: Run the analysis
```bash
# Quick test
python test_ocr.py

# Full analysis in Jupyter
jupyter notebook ocr_diag.ipynb
```

## What the Notebook Does

The `ocr_diag.ipynb` notebook performs comprehensive OCR analysis:

1. **Basic Setup**: Configures Tesseract path and loads the image
2. **Initial OCR**: Tests basic OCR on the original image
3. **Image Preprocessing**: Applies various preprocessing techniques:
   - Grayscale conversion
   - Binary thresholding
   - Contrast enhancement
   - Image resizing
   - Morphological operations
4. **Advanced OCR**: Tests different Tesseract configurations:
   - Different Page Segmentation Modes (PSM)
   - Character whitelisting
   - OCR Engine Modes (OEM)
5. **Combination Testing**: Tests combinations of preprocessing + configurations
6. **Results Summary**: Shows which methods successfully extract "R768"
7. **Visualization**: Displays processed images for visual inspection

## Expected Output

The notebook will systematically test different approaches until it finds one that successfully extracts "R768" from the image. It will report:

- ✅ Successful methods that extract "R768"
- ⚠️ Methods that extract different text
- ❌ Methods that fail with errors

## Troubleshooting

### Common Issues

1. **"Python not found"**
   - Make sure Anaconda is properly installed
   - Use Anaconda Prompt instead of regular command prompt

2. **"NumPy version compatibility error"**
   - Run: `conda install numpy=1.24 -y`
   - Or: `pip install numpy==1.24.3`

3. **"Tesseract not found"**
   - Verify Tesseract is installed at: `C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe`
   - Update the path in the notebook if different

4. **"diag.png not found"**
   - Ensure the image file is in the same directory as the notebook
   - Check the file name and extension

5. **OCR returns empty or wrong text**
   - The notebook will automatically try different preprocessing methods
   - Check the image quality and content
   - Try manual image editing if needed

### Manual Verification

To manually verify your setup:

```python
import pytesseract
from PIL import Image
import os

# Check Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'
print("Tesseract version:", pytesseract.get_tesseract_version())

# Check image
if os.path.exists('diag.png'):
    img = Image.open('diag.png')
    print(f"Image size: {img.size}")
    text = pytesseract.image_to_string(img)
    print(f"OCR result: '{text.strip()}'")
```

## Next Steps

After running the notebook:

1. Review the results summary to see which methods worked
2. If "R768" was successfully extracted, you're done!
3. If not, you may need to:
   - Manually inspect the image
   - Try additional preprocessing
   - Verify the image actually contains "R768"
   - Check image quality and resolution

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Try running `python test_ocr.py` for a quick diagnostic
4. Review the detailed output in the Jupyter notebook
