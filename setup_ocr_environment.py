#!/usr/bin/env python3
"""
Setup script for OCR environment
This script helps set up the required packages for OCR analysis.
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully!")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def main():
    print("🚀 Setting up OCR Environment")
    print("=" * 50)
    
    # Check if we're in a conda environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ Conda environment detected: {conda_env}")
    else:
        print("⚠️ No conda environment detected. Trying to use base environment.")
    
    # Try to fix NumPy compatibility issue first
    print("\n🔧 Fixing NumPy compatibility...")
    
    # Option 1: Try to downgrade NumPy
    commands_to_try = [
        ("conda install numpy=1.24 -y", "Downgrading NumPy to 1.24"),
        ("pip install numpy==1.24.3", "Installing NumPy 1.24.3 via pip"),
        ("conda install -c conda-forge pytesseract opencv pillow matplotlib jupyter -y", "Installing OCR packages"),
        ("pip install pytesseract opencv-python pillow matplotlib jupyter", "Installing OCR packages via pip"),
    ]
    
    success_count = 0
    for command, description in commands_to_try:
        if run_command(command, description):
            success_count += 1
        else:
            print(f"⚠️ Trying alternative approach...")
    
    print(f"\n📊 Setup Summary: {success_count}/{len(commands_to_try)} commands succeeded")
    
    # Test the installation
    print("\n🧪 Testing installation...")
    try:
        import pytesseract
        from PIL import Image
        print("✅ pytesseract and PIL imported successfully!")
        
        # Test Tesseract path
        tesseract_path = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'
        if os.path.exists(tesseract_path):
            print(f"✅ Tesseract executable found at: {tesseract_path}")
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        else:
            print(f"⚠️ Tesseract executable not found at: {tesseract_path}")
            print("Please verify the Tesseract installation path.")
        
        # Test image loading
        if os.path.exists('diag.png'):
            print("✅ diag.png found!")
            img = Image.open('diag.png')
            print(f"✅ Image loaded successfully! Size: {img.size}")
        else:
            print("⚠️ diag.png not found in current directory")
        
        print("\n🎉 Setup appears to be successful!")
        print("You can now run:")
        print("1. python test_ocr.py - for quick OCR test")
        print("2. jupyter notebook ocr_diag.ipynb - for detailed analysis")
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        print("\n🔧 Manual setup required:")
        print("1. Open Anaconda Prompt")
        print("2. Run: conda create -n ocr python=3.9")
        print("3. Run: conda activate ocr")
        print("4. Run: conda install -c conda-forge pytesseract opencv pillow matplotlib jupyter")
        print("5. Run: jupyter notebook ocr_diag.ipynb")

if __name__ == "__main__":
    main()
